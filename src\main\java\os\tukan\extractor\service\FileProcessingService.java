package os.tukan.extractor.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import lombok.extern.log4j.Log4j2;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PreDestroy;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Log4j2
@Service
public class FileProcessingService {

    private static final int DEFAULT_CHUNK_SIZE = 1000;
    private static final int DEFAULT_OVERLAP = 100;
    private static final int MAX_CHUNK_SIZE = 2000;
    private static final int MIN_CHUNK_SIZE = 200;
    private static final long MAX_FILE_SIZE = 100L * 1024 * 1024; // 100MB

    private final ObjectMapper objectMapper;
    private final ExecutorService executorService;

    public FileProcessingService() {
        this.objectMapper = new ObjectMapper();
        this.executorService = Executors.newFixedThreadPool(
            Math.min(Runtime.getRuntime().availableProcessors(), 4)
        );
    }    public long calculateDatasetSize(MultipartFile[] files) {
        if (files == null || files.length == 0) return 0L;
        return Arrays.stream(files)
            .filter(Objects::nonNull)
            .filter(file -> !file.isEmpty())
            .mapToLong(MultipartFile::getSize)
            .sum();
    }

    public long calculateDatasetSize(MultipartFile file) {
        return (file == null || file.isEmpty()) ? 0L : file.getSize();
    }    public List<String> processDatasetFile(MultipartFile file) throws IOException {
        if (file == null || file.isEmpty() || file.getOriginalFilename() == null) {
            throw new IOException("Invalid file");
        }

        String extension = getFileExtension(file.getOriginalFilename()).toLowerCase();
        return switch (extension) {
            case "pdf" -> processPdfDataset(file);
            case "txt" -> processTextDataset(file);
            case "json" -> processJsonDataset(file);
            default -> throw new IOException("Unsupported format: " + extension);
        };
    }    public List<String> processMultipleDatasetFiles(MultipartFile[] files) throws IOException {
        if (files == null || files.length == 0) {
            throw new IOException("No files provided");
        }

        List<MultipartFile> pdfFiles = Arrays.stream(files)
            .filter(file -> file != null && !file.isEmpty() && file.getOriginalFilename() != null)
            .filter(file -> file.getOriginalFilename().toLowerCase().endsWith(".pdf"))
            .filter(file -> {
                if (file.getSize() > MAX_FILE_SIZE) {
                    log.warn("File {} exceeds size limit", file.getOriginalFilename());
                    return false;
                }
                return true;
            })
            .collect(Collectors.toList());

        if (pdfFiles.isEmpty()) {
            throw new IOException("No valid PDF files found");
        }

        List<CompletableFuture<List<String>>> futures = pdfFiles.stream()
            .map(file -> CompletableFuture.supplyAsync(() -> {
                try {
                    List<String> documents = processPdfDataset(file);
                    String filename = getFileNameWithoutExtension(file.getOriginalFilename());
                    return documents.stream()
                        .map(doc -> String.format("[%s] %s", filename, doc))
                        .collect(Collectors.toList());
                } catch (Exception e) {
                    log.error("Failed to process: {}", file.getOriginalFilename(), e);
                    return new ArrayList<String>();
                }
            }, executorService))
            .collect(Collectors.toList());

        List<String> allDocuments = new ArrayList<>();
        for (CompletableFuture<List<String>> future : futures) {
            try {
                allDocuments.addAll(future.get(30, TimeUnit.SECONDS));
            } catch (Exception e) {
                log.error("Processing timeout", e);
            }
        }

        if (allDocuments.isEmpty()) {
            throw new IOException("No documents processed successfully");
        }

        return allDocuments;
    }    public GroundTruthResult processGroundTruthFile(MultipartFile file) throws IOException {
        if (file == null || file.isEmpty() || file.getOriginalFilename() == null) {
            throw new IOException("Invalid ground truth file");
        }

        if (!getFileExtension(file.getOriginalFilename()).toLowerCase().equals("json")) {
            throw new IOException("Ground truth file must be JSON format");
        }

        try {
            String content = new String(file.getBytes(), StandardCharsets.UTF_8);
            JsonNode rootNode = objectMapper.readTree(content);

            if (rootNode.has("queries") && rootNode.has("ground_truth")) {
                return processCombinedDatasetFormat(rootNode);
            } else {
                return processSimpleGroundTruthFormat(content);
            }
        } catch (Exception e) {
            throw new IOException("Failed to parse ground truth JSON: " + e.getMessage(), e);
        }
    }    private GroundTruthResult processCombinedDatasetFormat(JsonNode rootNode) throws IOException {
        try {
            JsonNode queriesNode = rootNode.get("queries");
            List<String> queries = new ArrayList<>();
            if (queriesNode.isArray()) {
                for (JsonNode queryNode : queriesNode) {
                    if (queryNode.isTextual()) {
                        queries.add(queryNode.asText());
                    }
                }
            }

            JsonNode groundTruthNode = rootNode.get("ground_truth");
            TypeReference<Map<String, List<String>>> typeRef = new TypeReference<>() {};
            Map<String, List<String>> groundTruth = objectMapper.convertValue(groundTruthNode, typeRef);

            return new GroundTruthResult(queries, groundTruth);
        } catch (Exception e) {
            throw new IOException("Failed to parse combined dataset format: " + e.getMessage(), e);
        }
    }

    private GroundTruthResult processSimpleGroundTruthFormat(String content) throws IOException {
        try {
            TypeReference<Map<String, List<String>>> typeRef = new TypeReference<>() {};
            Map<String, List<String>> groundTruth = objectMapper.readValue(content, typeRef);
            List<String> queries = new ArrayList<>(groundTruth.keySet());
            return new GroundTruthResult(queries, groundTruth);
        } catch (Exception e) {
            throw new IOException("Failed to parse simple ground truth format: " + e.getMessage(), e);
        }
    }    @Getter
    public static class GroundTruthResult {
        private final List<String> queries;
        private final Map<String, List<String>> groundTruth;

        public GroundTruthResult(List<String> queries, Map<String, List<String>> groundTruth) {
            this.queries = queries;
            this.groundTruth = groundTruth;
        }
    }    private List<String> processPdfDataset(MultipartFile file) throws IOException {
        try (InputStream inputStream = file.getInputStream();
             PDDocument document = Loader.loadPDF(inputStream.readAllBytes())) {

            PDFTextStripper textStripper = new PDFTextStripper();
            String fullText = textStripper.getText(document);
            
            return chunkTextContentAware(fullText);

        } catch (IOException e) {
            throw new IOException("Failed to process PDF: " + e.getMessage(), e);
        }
    }

    private List<String> chunkTextContentAware(String fullText) {
        List<String> paragraphChunks = chunkByParagraphs(fullText);
        if (isOptimalChunkSize(paragraphChunks)) {
            return paragraphChunks;
        }
        
        List<String> sentenceChunks = chunkBySentences(fullText);
        if (isOptimalChunkSize(sentenceChunks)) {
            return sentenceChunks;
        }
        
        return chunkBySemanticSlidingWindow(fullText);
    }

    private List<String> chunkByParagraphs(String text) {
        List<String> chunks = new ArrayList<>();
        String[] paragraphs = text.split("\\n\\s*\\n");
        StringBuilder currentChunk = new StringBuilder();
        
        for (String paragraph : paragraphs) {
            String trimmedParagraph = paragraph.trim();
            if (trimmedParagraph.isEmpty()) continue;
            
            if (currentChunk.length() + trimmedParagraph.length() + 2 > MAX_CHUNK_SIZE) {
                if (currentChunk.length() >= MIN_CHUNK_SIZE) {
                    chunks.add(currentChunk.toString().trim());
                    String overlap = getLastSentences(currentChunk.toString(), DEFAULT_OVERLAP);
                    currentChunk = new StringBuilder(overlap.isEmpty() ? "" : overlap + " ");
                } else {
                    currentChunk = new StringBuilder();
                }
            }
            
            currentChunk.append(trimmedParagraph).append("\n\n");
        }
        
        if (currentChunk.length() >= MIN_CHUNK_SIZE) {
            chunks.add(currentChunk.toString().trim());
        }
        
        return chunks;
    }

    private List<String> chunkBySentences(String text) {
        List<String> chunks = new ArrayList<>();
        String[] sentences = text.split("(?<=[.!?])\\s+");
        StringBuilder currentChunk = new StringBuilder();
        
        for (String sentence : sentences) {
            String trimmedSentence = sentence.trim();
            if (trimmedSentence.isEmpty()) continue;
            
            if (currentChunk.length() + trimmedSentence.length() + 1 > MAX_CHUNK_SIZE) {
                if (currentChunk.length() >= MIN_CHUNK_SIZE) {
                    chunks.add(currentChunk.toString().trim());
                    String overlap = getLastSentences(currentChunk.toString(), DEFAULT_OVERLAP);
                    currentChunk = new StringBuilder(overlap.isEmpty() ? "" : overlap + " ");
                } else {
                    currentChunk = new StringBuilder();
                }
            }
            
            currentChunk.append(trimmedSentence).append(" ");
        }
        
        if (currentChunk.length() >= MIN_CHUNK_SIZE) {
            chunks.add(currentChunk.toString().trim());
        }
        
        return chunks;
    }

    private List<String> chunkBySemanticSlidingWindow(String text) {
        List<String> chunks = new ArrayList<>();
        int start = 0;
        
        while (start < text.length()) {
            int end = Math.min(start + DEFAULT_CHUNK_SIZE, text.length());
            
            if (end < text.length()) {
                int lastSentenceEnd = Math.max(
                    Math.max(text.lastIndexOf('.', end), text.lastIndexOf('?', end)),
                    text.lastIndexOf('!', end)
                );
                if (lastSentenceEnd > start + MIN_CHUNK_SIZE) {
                    end = lastSentenceEnd + 1;
                }
            }
            
            String chunk = text.substring(start, end).trim();
            if (!chunk.isEmpty() && chunk.length() >= MIN_CHUNK_SIZE) {
                chunks.add(chunk);
            }
            
            start = Math.max(start + DEFAULT_CHUNK_SIZE - DEFAULT_OVERLAP, end);
        }
        
        return chunks;
    }

    private boolean isOptimalChunkSize(List<String> chunks) {
        if (chunks.isEmpty()) return false;
        double avgLength = chunks.stream().mapToInt(String::length).average().orElse(0);
        return avgLength >= MIN_CHUNK_SIZE && avgLength <= MAX_CHUNK_SIZE;
    }

    private String getLastSentences(String text, int maxLength) {
        String[] sentences = text.split("(?<=[.!?])\\s+");
        StringBuilder result = new StringBuilder();
        
        for (int i = sentences.length - 1; i >= 0; i--) {
            if (result.length() + sentences[i].length() > maxLength) {
                break;
            }
            result.insert(0, sentences[i] + " ");
        }
        
        return result.toString().trim();
    }    private List<String> processTextDataset(MultipartFile file) throws IOException {
        try {
            String content = new String(file.getBytes(), StandardCharsets.UTF_8);
            return Arrays.stream(content.split("\n"))
                    .map(String::trim)
                    .filter(line -> !line.isEmpty())
                    .toList();
        } catch (Exception e) {
            throw new IOException("Failed to process text dataset: " + e.getMessage(), e);
        }
    }

    private List<String> processJsonDataset(MultipartFile file) throws IOException {
        try {
            String content = new String(file.getBytes(), StandardCharsets.UTF_8);
            JsonNode jsonNode = objectMapper.readTree(content);
            List<String> documents = new ArrayList<>();

            if (jsonNode.isArray()) {
                for (JsonNode node : jsonNode) {
                    if (node.isTextual()) {
                        documents.add(node.asText());
                    }
                }
            } else if (jsonNode.isObject()) {
                jsonNode.fields().forEachRemaining(entry -> {
                    if (entry.getValue().isTextual()) {
                        documents.add(entry.getValue().asText());
                    }
                });
            }

            return documents;
        } catch (Exception e) {
            throw new IOException("Failed to process JSON dataset: " + e.getMessage(), e);
        }
    }

    @PreDestroy
    public void cleanup() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    private String getFileExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return "";
        }
        return filename.substring(lastDotIndex + 1);
    }

    private String getFileNameWithoutExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return filename;
        }
        return filename.substring(0, lastDotIndex);
    }
}
