package os.tukan.extractor.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import lombok.extern.log4j.Log4j2;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;

@Log4j2
@Service
public class FileProcessingService {

    private final ObjectMapper objectMapper;

    public FileProcessingService() {
        this.objectMapper = new ObjectMapper();
    }

    /**
     * Calculate total size of dataset files in bytes
     */
    public long calculateDatasetSize(MultipartFile[] files) {
        if (files == null || files.length == 0) {
            return 0L;
        }

        long totalSize = 0L;
        for (MultipartFile file : files) {
            if (file != null && !file.isEmpty()) {
                totalSize += file.getSize();
            }
        }
        return totalSize;
    }

    /**
     * Calculate size of a single dataset file in bytes
     */
    public long calculateDatasetSize(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return 0L;
        }
        return file.getSize();
    }

    /**
     * Process dataset file - supports PDF, TXT, and JSON formats
     */
    public List<String> processDatasetFile(MultipartFile file) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new IOException("File is null or empty");
        }

        String filename = file.getOriginalFilename();
        if (filename == null) {
            throw new IOException("Filename is null");
        }

        String extension = getFileExtension(filename).toLowerCase();

        return switch (extension) {
            case "pdf" -> processPdfDataset(file);
            case "txt" -> processTextDataset(file);
            case "json" -> processJsonDataset(file);
            default ->
                throw new IOException("Unsupported file format: " + extension + ". Supported formats: PDF, TXT, JSON");
        };
    }

    /**
     * Process multiple dataset files - supports multiple PDFs, TXT, and JSON
     * formats. When processing directory uploads, only PDF files are processed.
     */
    public List<String> processMultipleDatasetFiles(MultipartFile[] files) throws IOException {
        return processMultipleDatasetFiles(files, false);
    }

    /**
     * Process multiple dataset files with option to filter only PDF files
     * @param files Array of files to process
     * @param pdfOnly If true, only process PDF files (used for directory uploads)
     */
    public List<String> processMultipleDatasetFiles(MultipartFile[] files, boolean pdfOnly) throws IOException {
        if (files == null || files.length == 0) {
            throw new IOException("No files provided");
        }

        List<String> allDocuments = new ArrayList<>();
        int processedFiles = 0;
        int pdfFileCount = 0;
        int skippedFileCount = 0;

        for (MultipartFile file : files) {
            if (file == null || file.isEmpty()) {
                continue;
            }

            String filename = file.getOriginalFilename();
            if (filename == null) {
                continue;
            }

            String extension = getFileExtension(filename).toLowerCase();

            // If pdfOnly is true (directory upload), skip non-PDF files
            if (pdfOnly && !"pdf".equals(extension)) {
                skippedFileCount++;
                continue;
            }

            try {
                List<String> documents;
                switch (extension) {
                    case "pdf":
                        documents = processPdfDataset(file);
                        pdfFileCount++;
                        break;
                    case "txt":
                        if (pdfOnly) {
                            skippedFileCount++;
                            continue;
                        }
                        documents = processTextDataset(file);
                        break;
                    case "json":
                        if (pdfOnly) {
                            skippedFileCount++;
                            continue;
                        }
                        documents = processJsonDataset(file);
                        break;
                    default:
                        skippedFileCount++;
                        continue;
                }

                // Add filename prefix to distinguish documents from different files
                for (String document : documents) {
                    String documentWithSource = String.format("[%s] %s",
                            getFileNameWithoutExtension(filename), document);
                    allDocuments.add(documentWithSource);
                }
                processedFiles++;

            } catch (Exception e) {
                // Continue processing other files instead of failing completely
            }
        }

        if (processedFiles == 0) {
            if (pdfOnly && skippedFileCount > 0) {
                throw new IOException(String.format("No PDF files found in the selected directory. Found %d non-PDF files that were skipped.", skippedFileCount));
            } else {
                throw new IOException("No files could be processed successfully");
            }
        }

        return allDocuments;
    }

    /**
     * Get statistics about processed files for directory uploads
     */
    public static class FileProcessingStats {
        private final int totalFiles;
        private final int processedFiles;
        private final int pdfFiles;
        private final int skippedFiles;

        public FileProcessingStats(int totalFiles, int processedFiles, int pdfFiles, int skippedFiles) {
            this.totalFiles = totalFiles;
            this.processedFiles = processedFiles;
            this.pdfFiles = pdfFiles;
            this.skippedFiles = skippedFiles;
        }

        public int getTotalFiles() { return totalFiles; }
        public int getProcessedFiles() { return processedFiles; }
        public int getPdfFiles() { return pdfFiles; }
        public int getSkippedFiles() { return skippedFiles; }
    }

    /**
     * Process ground truth file - supports JSON format
     * Expected formats:
     * 1. Simple: {"query1": ["doc1", "doc2"], "query2": ["doc3"]}
     * 2. Combined dataset: {"queries": [...], "ground_truth": {...}, ...}
     */
    public GroundTruthResult processGroundTruthFile(MultipartFile file) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new IOException("File is null or empty");
        }

        String filename = file.getOriginalFilename();
        if (filename == null) {
            throw new IOException("Filename is null");
        }

        String extension = getFileExtension(filename).toLowerCase();

        if (!"json".equals(extension)) {
            throw new IOException("Ground truth file must be in JSON format");
        }

        try {
            String content = new String(file.getBytes(), StandardCharsets.UTF_8);
            JsonNode rootNode = objectMapper.readTree(content);

            // Check if this is a combined dataset format
            if (rootNode.has("queries") && rootNode.has("ground_truth")) {
                return processCombinedDatasetFormat(rootNode);
            } else {
                // Assume simple ground truth format
                return processSimpleGroundTruthFormat(content);
            }

        } catch (Exception e) {
            throw new IOException("Failed to parse ground truth JSON: " + e.getMessage(), e);
        }
    }

    private GroundTruthResult processCombinedDatasetFormat(JsonNode rootNode) throws IOException {
        try {
            // Extract queries
            JsonNode queriesNode = rootNode.get("queries");
            List<String> queries = new ArrayList<>();
            if (queriesNode.isArray()) {
                for (JsonNode queryNode : queriesNode) {
                    if (queryNode.isTextual()) {
                        queries.add(queryNode.asText());
                    }
                }
            }

            // Extract ground truth
            JsonNode groundTruthNode = rootNode.get("ground_truth");
            TypeReference<Map<String, List<String>>> typeRef = new TypeReference<>() {};
            Map<String, List<String>> groundTruth = objectMapper.convertValue(groundTruthNode, typeRef);

            return new GroundTruthResult(queries, groundTruth);

        } catch (Exception e) {
            throw new IOException("Failed to parse combined dataset format: " + e.getMessage(), e);
        }
    }

    private GroundTruthResult processSimpleGroundTruthFormat(String content) throws IOException {
        try {
            TypeReference<Map<String, List<String>>> typeRef = new TypeReference<>() {};
            Map<String, List<String>> groundTruth = objectMapper.readValue(content, typeRef);

            // Extract queries from ground truth keys
            List<String> queries = new ArrayList<>(groundTruth.keySet());

            return new GroundTruthResult(queries, groundTruth);

        } catch (Exception e) {
            throw new IOException("Failed to parse simple ground truth format: " + e.getMessage(), e);
        }
    }

    @Getter
    public static class GroundTruthResult {
        private final List<String> queries;
        private final Map<String, List<String>> groundTruth;

        public GroundTruthResult(List<String> queries, Map<String, List<String>> groundTruth) {
            this.queries = queries;
            this.groundTruth = groundTruth;
        }

    }

    private List<String> processPdfDataset(MultipartFile file) throws IOException {
        List<String> documents = new ArrayList<>();

        try (InputStream inputStream = file.getInputStream();
                PDDocument document = Loader.loadPDF(inputStream.readAllBytes())) {

            PDFTextStripper textStripper = new PDFTextStripper();
            int numberOfPages = document.getNumberOfPages();

            // Extract text page by page
            for (int i = 1; i <= numberOfPages; i++) {
                textStripper.setStartPage(i);
                textStripper.setEndPage(i);
                String pageText = textStripper.getText(document).trim();

                if (!pageText.isEmpty()) {
                    documents.add(pageText);
                }
            }

            return documents;

        } catch (IOException e) {
            throw new IOException("Failed to process PDF dataset: " + e.getMessage(), e);
        }
    }

    private List<String> processTextDataset(MultipartFile file) throws IOException {
        try {
            String content = new String(file.getBytes(), StandardCharsets.UTF_8);

            return Arrays.stream(content.split("\n"))
                    .map(String::trim)
                    .filter(line -> !line.isEmpty())
                    .toList();

        } catch (Exception e) {
            throw new IOException("Failed to process text dataset: " + e.getMessage(), e);
        }
    }

    private List<String> processJsonDataset(MultipartFile file) throws IOException {
        try {
            String content = new String(file.getBytes(), StandardCharsets.UTF_8);
            JsonNode jsonNode = objectMapper.readTree(content);

            List<String> documents = new ArrayList<>();

            if (jsonNode.isArray()) {
                // Array of strings
                for (JsonNode node : jsonNode) {
                    if (node.isTextual()) {
                        documents.add(node.asText());
                    }
                }
            } else if (jsonNode.isObject()) {
                // Object with documents as values
                jsonNode.fields().forEachRemaining(entry -> {
                    if (entry.getValue().isTextual()) {
                        documents.add(entry.getValue().asText());
                    }
                });
            }

            return documents;

        } catch (Exception e) {
            throw new IOException("Failed to process JSON dataset: " + e.getMessage(), e);
        }
    }

    private String getFileExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return "";
        }
        return filename.substring(lastDotIndex + 1);
    }

    private String getFileNameWithoutExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return filename;
        }
        return filename.substring(0, lastDotIndex);
    }
}
