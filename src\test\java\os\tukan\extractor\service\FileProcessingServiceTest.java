package os.tukan.extractor.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class FileProcessingServiceTest {

    private FileProcessingService fileProcessingService;

    @Mock
    private MultipartFile mockPdfFile;

    @Mock
    private MultipartFile mockTxtFile;

    @Mock
    private MultipartFile mockJsonFile;

    @Mock
    private MultipartFile mockImageFile;

    @BeforeEach
    void setUp() {
        fileProcessingService = new FileProcessingService();
    }

    @Test
    void testProcessMultipleDatasetFiles_MixedFiles_ProcessesAllSupportedTypes() throws IOException {
        // Arrange
        MockMultipartFile pdfFile = new MockMultipartFile(
                "file", "test.pdf", "application/pdf", "PDF content".getBytes());
        MockMultipartFile txtFile = new MockMultipartFile(
                "file", "test.txt", "text/plain", "Line 1\nLine 2\nLine 3".getBytes());
        MockMultipartFile jsonFile = new MockMultipartFile(
                "file", "test.json", "application/json", "[\"doc1\", \"doc2\"]".getBytes());

        MultipartFile[] files = {pdfFile, txtFile, jsonFile};

        // Act
        List<String> result = fileProcessingService.processMultipleDatasetFiles(files, false);

        // Assert
        assertNotNull(result);
        assertTrue(result.size() > 0);
        // Should contain documents from all three files with prefixes
        assertTrue(result.stream().anyMatch(doc -> doc.contains("[test]")));
    }

    @Test
    void testProcessMultipleDatasetFiles_PdfOnlyMode_FiltersNonPdfFiles() throws IOException {
        // Arrange
        MockMultipartFile pdfFile = new MockMultipartFile(
                "file", "document.pdf", "application/pdf", "PDF content".getBytes());
        MockMultipartFile txtFile = new MockMultipartFile(
                "file", "document.txt", "text/plain", "Text content".getBytes());
        MockMultipartFile imageFile = new MockMultipartFile(
                "file", "image.jpg", "image/jpeg", "Image content".getBytes());

        MultipartFile[] files = {pdfFile, txtFile, imageFile};

        // Act
        List<String> result = fileProcessingService.processMultipleDatasetFiles(files, true);

        // Assert
        assertNotNull(result);
        // Should only process PDF files
        assertTrue(result.stream().allMatch(doc -> doc.contains("[document]")));
    }

    @Test
    void testProcessMultipleDatasetFiles_PdfOnlyMode_NoPdfFiles_ThrowsException() {
        // Arrange
        MockMultipartFile txtFile = new MockMultipartFile(
                "file", "document.txt", "text/plain", "Text content".getBytes());
        MockMultipartFile imageFile = new MockMultipartFile(
                "file", "image.jpg", "image/jpeg", "Image content".getBytes());

        MultipartFile[] files = {txtFile, imageFile};

        // Act & Assert
        IOException exception = assertThrows(IOException.class, () -> {
            fileProcessingService.processMultipleDatasetFiles(files, true);
        });

        assertTrue(exception.getMessage().contains("No PDF files found"));
        assertTrue(exception.getMessage().contains("2 non-PDF files"));
    }

    @Test
    void testProcessMultipleDatasetFiles_EmptyArray_ThrowsException() {
        // Arrange
        MultipartFile[] files = {};

        // Act & Assert
        IOException exception = assertThrows(IOException.class, () -> {
            fileProcessingService.processMultipleDatasetFiles(files, false);
        });

        assertEquals("No files provided", exception.getMessage());
    }

    @Test
    void testProcessMultipleDatasetFiles_NullArray_ThrowsException() {
        // Act & Assert
        IOException exception = assertThrows(IOException.class, () -> {
            fileProcessingService.processMultipleDatasetFiles(null, false);
        });

        assertEquals("No files provided", exception.getMessage());
    }

    @Test
    void testCalculateDatasetSize_MultipleFiles() {
        // Arrange
        MockMultipartFile file1 = new MockMultipartFile(
                "file", "test1.pdf", "application/pdf", new byte[1000]);
        MockMultipartFile file2 = new MockMultipartFile(
                "file", "test2.pdf", "application/pdf", new byte[2000]);
        MockMultipartFile file3 = new MockMultipartFile(
                "file", "test3.pdf", "application/pdf", new byte[3000]);

        MultipartFile[] files = {file1, file2, file3};

        // Act
        long totalSize = fileProcessingService.calculateDatasetSize(files);

        // Assert
        assertEquals(6000L, totalSize);
    }

    @Test
    void testCalculateDatasetSize_EmptyFiles() {
        // Arrange
        MultipartFile[] files = {};

        // Act
        long totalSize = fileProcessingService.calculateDatasetSize(files);

        // Assert
        assertEquals(0L, totalSize);
    }

    @Test
    void testCalculateDatasetSize_NullFiles() {
        // Act
        long totalSize = fileProcessingService.calculateDatasetSize((MultipartFile[]) null);

        // Assert
        assertEquals(0L, totalSize);
    }

    @Test
    void testFileProcessingStats_Creation() {
        // Act
        FileProcessingService.FileProcessingStats stats = 
                new FileProcessingService.FileProcessingStats(10, 8, 6, 2);

        // Assert
        assertEquals(10, stats.getTotalFiles());
        assertEquals(8, stats.getProcessedFiles());
        assertEquals(6, stats.getPdfFiles());
        assertEquals(2, stats.getSkippedFiles());
    }
}
