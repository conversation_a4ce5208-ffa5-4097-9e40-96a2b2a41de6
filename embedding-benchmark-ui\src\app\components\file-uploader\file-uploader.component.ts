import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-file-uploader',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './file-uploader.component.html',
  styleUrls: ['./file-uploader.component.scss']
})
export class FileUploaderComponent {
  @Input() title: string = '';
  @Input() description: string = '';
  @Input() acceptedTypes: string = '';
  @Input() uploadedFileName: string = '';
  @Input() allowMultiple: boolean = false;
  @Input() allowDirectory: boolean = false;
  @Output() fileUpload = new EventEmitter<File>();
  @Output() multipleFileUpload = new EventEmitter<File[]>();
  @Output() directoryUpload = new EventEmitter<File[]>();

  isDragOver = false;
  uploading = false;
  error = '';
  uploadedFiles: File[] = [];
  isDirectoryUpload = false;
  processingProgress = 0;
  processingMessage = '';

  onDragOver(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = true;
  }

  onDragLeave(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = false;
  }

  onDrop(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver = false;

    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
      // Check if this is a directory upload by looking at webkitRelativePath
      const isDirectoryUpload = !!(files[0].webkitRelativePath && files[0].webkitRelativePath.length > 0);
      this.isDirectoryUpload = isDirectoryUpload;

      if (isDirectoryUpload) {
        this.handleDirectoryUpload(Array.from(files));
      } else if (this.allowMultiple) {
        this.handleMultipleFiles(Array.from(files));
      } else {
        this.handleFile(files[0]);
      }
    }
  }

  onFileSelected(event: any) {
    const files = event.target.files;
    if (files && files.length > 0) {
      // Check if this is a directory upload by looking at webkitRelativePath
      const isDirectoryUpload = !!(files[0].webkitRelativePath && files[0].webkitRelativePath.length > 0);
      this.isDirectoryUpload = isDirectoryUpload;

      if (isDirectoryUpload) {
        this.handleDirectoryUpload(Array.from(files));
      } else if (this.allowMultiple) {
        this.handleMultipleFiles(Array.from(files));
      } else {
        this.handleFile(files[0]);
      }
    }
  }

  private handleFile(file: File) {
    this.error = '';

    // Validate file type
    if (this.acceptedTypes && !this.isFileTypeAccepted(file)) {
      this.error = `File type not supported. Accepted types: ${this.acceptedTypes}`;
      return;
    }

    // Validate file size (max 100MB)
    const maxSize = 100 * 1024 * 1024; // 100MB
    if (file.size > maxSize) {
      this.error = 'File size too large. Maximum size is 100MB.';
      return;
    }

    this.uploading = true;
    this.fileUpload.emit(file);

    // Reset uploading state after a delay (will be handled by parent component)
    setTimeout(() => {
      this.uploading = false;
    }, 1000);
  }

  private handleMultipleFiles(files: File[]) {
    this.error = '';
    const validFiles: File[] = [];
    const maxSize = 100 * 1024 * 1024; // 100MB per file

    for (const file of files) {
      // Validate file type
      if (this.acceptedTypes && !this.isFileTypeAccepted(file)) {
        this.error = `File ${file.name} type not supported. Accepted types: ${this.acceptedTypes}`;
        continue;
      }

      // Validate file size
      if (file.size > maxSize) {
        this.error = `File ${file.name} is too large. Maximum size is 100MB per file.`;
        continue;
      }

      validFiles.push(file);
    }

    if (validFiles.length === 0) {
      this.error = 'No valid files found.';
      return;
    }

    this.uploadedFiles = validFiles;
    this.uploading = true;
    this.multipleFileUpload.emit(validFiles);

    // Reset uploading state after a delay
    setTimeout(() => {
      this.uploading = false;
    }, 2000);
  }

  private handleDirectoryUpload(files: File[]) {
    this.error = '';
    this.processingProgress = 0;
    this.processingMessage = 'Scanning directory for PDF files...';

    const validFiles: File[] = [];
    const maxSize = 100 * 1024 * 1024; // 100MB per file
    let pdfFileCount = 0;
    let totalFileCount = files.length;

    // Process files with progress indication
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      this.processingProgress = Math.round((i / files.length) * 50); // First 50% for scanning

      // For directory uploads, only process PDF files
      if (!file.name.toLowerCase().endsWith('.pdf')) {
        continue;
      }

      // Validate file size
      if (file.size > maxSize) {
        this.error = `PDF file ${file.name} is too large. Maximum size is 100MB per file.`;
        continue;
      }

      validFiles.push(file);
      pdfFileCount++;
    }

    this.processingProgress = 50;
    this.processingMessage = `Found ${pdfFileCount} PDF files, preparing upload...`;

    if (validFiles.length === 0) {
      this.processingProgress = 0;
      this.processingMessage = '';
      if (pdfFileCount === 0) {
        this.error = `No PDF files found in the selected directory. Found ${totalFileCount} total files.`;
      } else {
        this.error = 'No valid PDF files found (all were too large).';
      }
      return;
    }

    this.uploadedFiles = validFiles;
    this.uploading = true;
    this.processingProgress = 100;
    this.processingMessage = `Uploading ${validFiles.length} PDF files...`;

    this.directoryUpload.emit(validFiles);

    // Reset uploading state after a delay
    setTimeout(() => {
      this.uploading = false;
      this.processingProgress = 0;
      this.processingMessage = '';
    }, 2000);
  }

  private isFileTypeAccepted(file: File): boolean {
    if (!this.acceptedTypes) return true;

    const acceptedExtensions = this.acceptedTypes.split(',').map(ext => ext.trim());
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();

    return acceptedExtensions.some(ext =>
      ext === fileExtension ||
      ext === file.type ||
      (ext.startsWith('.') && fileExtension === ext)
    );
  }
}
